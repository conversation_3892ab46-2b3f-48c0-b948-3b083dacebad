# import json
# from urllib.error import HTTPError
# from urllib.parse import urlencode
# from urllib.request import urlopen, Request

# #url = "https://servicedesk.grupoficohsa.hn:8443/api/v3/changes"
# url = "https://hnbnserdeskapp.gfficohsa.hn:8443/api/v3/changes/16763/tasks"

# headers = {
#     "Accept": "application/vnd.manageengine.sdp.v3+json",
#     "technician_key": "9C34CA68-8ABF-4C7D-9FD2-2C14522D0C65",  # Formato ManageEngine
#     "Content-Type":  "application/json"
# }

# input_data = {}

# # Probar sin input_data primero
# httprequest = Request(url, headers=headers, method='GET')

# try:
#     with urlopen(httprequest) as response:
#         print(response.read().decode())
# except HTTPError as e:
#     print(e.read().decode())

import json
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen, Request

#url = "https://servicedesk.grupoficohsa.hn:8443/api/v3/changes"
#url = "https://hnbnserdeskapp.gfficohsa.hn:8443/api/v3/changes/16763/tasks"
url = "https://hnbnserdeskapp.gfficohsa.hn:8443/api/v3/tasks/96816/comments?"

headers = {
    "Accept": "application/vnd.manageengine.sdp.v3+json",
    "technician_key": "9C34CA68-8ABF-4C7D-9FD2-2C14522D0C65",  # Formato ManageEngine
    "Content-Type":  "application/json"
}


input_data = '''{ 
    "list_info": { 
        "row_count": "12", 
        "start_index": 2, 
        "get_total_count": "true" 
        }
    }'''

url += urlencode({"input_data": input_data})

# Probar sin input_data primero
httprequest = Request(url, headers=headers, method='GET')

try:
    with urlopen(httprequest) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())

