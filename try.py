#Python version - 3.8

#Importar Librería requests
#python -m pip install requests


#This script requires requests module installed in python.
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen, Request


#Variable tipo long
id_cambio = 16763
token = "1861204D-3A37-418D-B0B6-1A7DC29DDDCE"


url = "/api/v3/changes"
headers ={"Accept": "application/vnd.manageengine.sdp.v3+json", 
          "Authorization" : f"authtoken: {token}", 
          "Content-Type" : "application/x-www-form-urlencoded"}
input_data = '''{}'''       
url += "?" + urlencode({"input_data":input_data})
httprequest = Request(url, headers=headers)
try:
    with urlopen(httprequest) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())